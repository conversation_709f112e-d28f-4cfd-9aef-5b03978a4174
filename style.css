/* Custom Styles for CREATIVE HYDRAULICS */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Navigation styles */
.nav-link {
    @apply text-gray-700 hover:text-royal-blue transition-colors duration-300 font-medium;
}

/* Button styles */
.btn-primary {
    @apply bg-royal-blue text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

.btn-secondary {
    @apply border-2 border-royal-blue text-royal-blue px-8 py-4 rounded-lg font-semibold hover:bg-royal-blue hover:text-white transition-all duration-300 transform hover:scale-105;
}

.btn-secondary-hero {
    @apply border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-royal-blue transition-all duration-300 transform hover:scale-105 backdrop-blur-sm;
}

/* Service card styles */
.service-card {
    @apply bg-white p-8 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100;
}

.service-icon {
    @apply w-16 h-16 bg-gradient-to-br from-royal-blue to-blue-600 rounded-full flex items-center justify-center text-white mb-6 mx-auto transform transition-transform duration-300;
}

.service-card:hover .service-icon {
    @apply scale-110;
}

/* Animation classes */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    animation: fadeInRight 0.8s ease-out forwards;
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    animation: fadeInLeft 0.8s ease-out forwards;
}

/* Keyframe animations */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Floating animation for hero elements */
.float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Navbar scroll effect */
.navbar-scrolled {
    @apply bg-white shadow-lg;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #4169E1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2c4fd6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fade-in-up,
    .fade-in-right,
    .fade-in-left {
        animation-delay: 0s !important;
    }
}

/* Hover effects for interactive elements */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Gradient text effect */
.gradient-text {
    background: linear-gradient(135deg, #4169E1, #D7263D);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Loading animation for icons */
.icon-spin {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Pulse effect for call-to-action elements */
.pulse-effect {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(65, 105, 225, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(65, 105, 225, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(65, 105, 225, 0);
    }
}

/* Section dividers */
.section-divider {
    height: 1px;
    background: linear-gradient(to right, transparent, #4169E1, transparent);
    margin: 2rem 0;
}

/* Card hover effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Text selection styling */
::selection {
    background-color: #4169E1;
    color: white;
}

::-moz-selection {
    background-color: #4169E1;
    color: white;
}

/* Focus styles for accessibility */
button:focus,
a:focus {
    outline: 2px solid #4169E1;
    outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* Hero section with background image */
#home {
    position: relative;
}

#home img {
    filter: brightness(0.7) contrast(1.1);
    transition: all 0.3s ease;
}

/* Enhanced backdrop blur for hero elements */
.backdrop-blur-sm {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* Remove transitions for reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
