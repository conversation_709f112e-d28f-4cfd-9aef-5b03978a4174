/* Elegant CSS for AI Code Assistant Landing Page */

/* Import Google Fonts - Elegant & Modern */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

/* Custom Properties - Elegant Color Scheme */
:root {
    --primary-white: #ffffff;
    --secondary-white: #f8f9fa;
    --light-gray: #f3f4f6;
    --medium-gray: #6b7280;
    --dark-gray: #374151;
    --royal-blue: #1e40af;
    --royal-purple: #7c3aed;
    --royal-gold: #d97706;
    --royal-navy: #1e293b;
    --elegant-black: #111827;
    --text-white: #ffffff;
    --text-light: #f3f4f6;
    --border-light: #e5e7eb;
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    line-height: 1.6;
    background: var(--elegant-black);
    color: var(--text-white);
    overflow-x: hidden;
}

/* Typography - Different Fonts for Different Elements */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    letter-spacing: -0.02em;
    color: var(--text-white);
}

.navbar-brand {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    letter-spacing: -0.03em;
    color: var(--text-white);
}

.gradient-text {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: var(--text-white);
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-weight: 800;
    letter-spacing: -0.03em;
    color: var(--text-white);
}

.section-title {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    letter-spacing: -0.02em;
    color: var(--text-white);
}

.feature-title {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    color: var(--text-white);
}

.pricing-title {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    color: var(--text-white);
}

.testimonial-name {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    color: var(--text-white);
}

.stats-number {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 700;
    letter-spacing: -0.05em;
    color: var(--text-white);
}

.tech-item {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 500;
    color: var(--text-white);
}

.btn-primary-modern,
.btn-secondary-modern {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: 0.02em;
}

.nav-link-modern {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    letter-spacing: 0.01em;
    color: var(--text-white);
}

/* Modern Navbar Styles */
.navbar-modern {
    background: var(--elegant-black);
    border-bottom: 1px solid var(--border-light);
    box-shadow: 0 1px 3px var(--shadow-light);
    transition: all 0.3s ease;
    position: relative;
}

.navbar-modern.scrolled {
    background: var(--elegant-black);
    box-shadow: 0 4px 6px var(--shadow-medium);
}

.navbar-brand {
    color: var(--text-white);
}

.navbar-logo {
    background: var(--royal-blue);
    border: 2px solid var(--royal-blue);
    color: var(--text-white);
}

.nav-link-modern {
    position: relative;
    color: var(--text-white) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
}

.nav-link-modern:hover {
    color: var(--royal-blue) !important;
    background: var(--light-gray);
    transform: translateY(-1px);
}

.nav-link-modern::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--royal-blue);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link-modern:hover::after {
    width: 80%;
}

.btn-primary-modern {
    background: var(--royal-blue);
    border: none;
    color: var(--text-white);
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px var(--shadow-light);
}

.btn-primary-modern:hover {
    background: var(--royal-navy);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-medium);
}

.btn-secondary-modern {
    background: var(--elegant-black);
    border: 2px solid var(--royal-blue);
    color: var(--text-white);
    font-weight: 600;
    padding: 10px 22px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-secondary-modern:hover {
    background: var(--royal-blue);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-medium);
}

/* Hero Section */
.hero-bg {
    background: linear-gradient(135deg, var(--elegant-black) 0%, var(--royal-navy) 100%);
    position: relative;
}

/* Feature Card Styles */
.feature-card {
    transition: all 0.3s ease;
    position: relative;
    background: var(--elegant-black);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: 0 2px 4px var(--shadow-light);
    color: var(--text-white);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px var(--shadow-medium);
    border-color: var(--royal-blue);
}

.feature-card .icon-container {
    position: relative;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: var(--royal-blue);
    color: var(--text-white);
}

.feature-card:hover .icon-container {
    background: var(--royal-navy);
}

/* Pricing Card Styles */
.pricing-card {
    transition: all 0.3s ease;
    position: relative;
    background: var(--elegant-black);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: 0 2px 4px var(--shadow-light);
    color: var(--text-white);
}

.pricing-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px var(--shadow-medium);
    border-color: var(--royal-blue);
}

.pricing-card.popular {
    border: 2px solid var(--royal-blue);
    box-shadow: 0 4px 8px var(--shadow-medium);
    background: var(--elegant-black);
}

/* Stats Section */
.stats-card {
    background: var(--elegant-black);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px var(--shadow-light);
    color: var(--text-white);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px var(--shadow-medium);
    border-color: var(--royal-blue);
}

.stats-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--text-white);
    margin-bottom: 0.5rem;
}

/* Testimonial Section */
.testimonial-card {
    background: var(--elegant-black);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 2rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px var(--shadow-light);
    color: var(--text-white);
}

.testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px var(--shadow-medium);
    border-color: var(--royal-blue);
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--royal-blue);
    color: var(--text-white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

/* Technology Stack */
.tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.tech-item {
    background: var(--elegant-black);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: var(--text-white);
    font-weight: 500;
    transition: all 0.3s ease;
}

.tech-item:hover {
    background: var(--light-gray);
    transform: translateY(-2px);
    border-color: var(--royal-blue);
    box-shadow: 0 4px 8px var(--shadow-light);
}

/* Sections */
section {
    padding: 4rem 0;
    color: var(--text-white);
}

section:nth-child(even) {
    background: var(--royal-navy);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 3rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .navbar-modern {
        padding: 0.5rem 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--royal-blue);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--royal-navy);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Focus Styles */
button:focus,
a:focus {
    outline: 2px solid var(--royal-blue);
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-white: #1f2937;
        --secondary-white: #111827;
        --light-gray: #374151;
        --medium-gray: #9ca3af;
        --dark-gray: #d1d5db;
        --text-white: #f9fafb;
        --text-light: #d1d5db;
        --border-light: #374151;
        --shadow-light: rgba(0, 0, 0, 0.2);
        --shadow-medium: rgba(0, 0, 0, 0.3);
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none;
    }
    
    body {
        background: white;
        color: black;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --royal-blue: #0000ff;
        --royal-navy: #000080;
        --text-white: #ffffff;
        --border-light: #ffffff;
    }
}
