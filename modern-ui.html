<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NEXUS CORE - Advanced Neural Interface System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="modern-ui.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'cyber-blue': '#00FFFF',
                        'cyber-purple': '#FF00FF',
                        'cyber-green': '#00FF41',
                        'neon-pink': '#FF0080',
                        'electric-blue': '#0080FF',
                        'matrix-green': '#00FF00',
                        'dark-void': '#000011',
                        'dark-space': '#0A0A1A',
                        'dark-nebula': '#1A0A2E',
                    },
                    fontFamily: {
                        'orbitron': ['Orbitron', 'monospace'],
                        'rajdhani': ['Rajdhani', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-rajdhani bg-dark-void text-white overflow-x-hidden">
    <!-- Sci-Fi Background Effects -->
    <div class="fixed inset-0 z-0">
        <div class="stars"></div>
        <div class="nebula"></div>
        <div class="scanning-lines"></div>
    </div>

    <!-- Navigation -->
    <nav class="fixed w-full bg-dark-void/90 backdrop-blur-lg z-50 border-b border-cyber-blue/30 nav-glow">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-cyber-blue to-neon-pink rounded-lg flex items-center justify-center neural-glow">
                        <i class="fas fa-brain text-white text-lg pulse-glow"></i>
                    </div>
                    <span class="text-2xl font-orbitron font-bold cyber-text">NEXUS CORE</span>
                    <span class="bg-matrix-green/20 text-matrix-green px-3 py-1 rounded border border-matrix-green/50 text-xs font-medium font-orbitron glow-text">NEURAL</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#systems" class="nav-link-sci">SYSTEMS</a>
                    <a href="#protocols" class="nav-link-sci">PROTOCOLS</a>
                    <a href="#interface" class="nav-link-sci">INTERFACE</a>
                    <a href="#access" class="nav-link-sci">ACCESS</a>
                    <a href="#core" class="nav-link-sci">CORE</a>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-cyber-blue hover:text-white transition-colors font-orbitron">CONNECT</button>
                    <button class="bg-gradient-to-r from-neon-pink to-cyber-purple hover:from-cyber-purple hover:to-neon-pink px-6 py-2 rounded-lg font-medium transition-all duration-300 font-orbitron glow-button">
                        INITIALIZE
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center relative pt-20 overflow-hidden">
        <!-- Sci-Fi Background Elements -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-64 h-64 bg-cyber-blue/20 rounded-full blur-3xl pulse-orb"></div>
            <div class="absolute bottom-20 right-10 w-96 h-96 bg-neon-pink/20 rounded-full blur-3xl pulse-orb-delayed"></div>
            <div class="absolute top-1/2 left-1/2 w-32 h-32 bg-matrix-green/30 rounded-full blur-2xl transform -translate-x-1/2 -translate-y-1/2 pulse-orb-slow"></div>
            <div class="cyber-grid"></div>
            <div class="data-streams"></div>
        </div>

        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center max-w-6xl mx-auto">
                <!-- Floating Sci-Fi UI Elements -->
                <div class="floating-elements">
                    <div class="floating-card neural-link">
                        <i class="fas fa-microchip"></i>
                        <span>NEURAL LINK</span>
                        <div class="status-indicator active"></div>
                    </div>
                    <div class="floating-card quantum-core">
                        <span>QUANTUM CORE</span>
                        <div class="quantum-particles"></div>
                    </div>
                    <div class="floating-card bio-sync">
                        <span>BIO-SYNC ACTIVE</span>
                        <div class="sync-waves"></div>
                    </div>
                    <div class="floating-card data-stream">
                        <i class="fas fa-stream"></i>
                        <span>DATA STREAM</span>
                        <div class="stream-lines"></div>
                    </div>
                </div>

                <!-- Main Heading -->
                <h1 class="text-6xl md:text-8xl font-orbitron font-black mb-8 leading-tight cyber-title">
                    NEURAL <span class="cyber-gradient">INTERFACE</span>
                    <br>
                    <span class="cyber-gradient-alt">PROTOCOL</span> ACTIVE
                </h1>

                <!-- Subheading -->
                <div class="mb-12">
                    <p class="text-xl text-cyber-blue mb-2 font-orbitron tracking-wider">ADVANCED CONSCIOUSNESS INTEGRATION</p>
                    <p class="text-xl text-matrix-green font-orbitron tracking-wider">QUANTUM PROCESSING ENABLED</p>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                    <button class="btn-neural">
                        <span>INITIALIZE NEURAL LINK</span>
                        <div class="neural-pulse"></div>
                    </button>
                    <button class="btn-quantum">
                        <span>ACCESS QUANTUM CORE</span>
                        <div class="quantum-glow"></div>
                    </button>
                </div>

                <!-- System Status -->
                <div class="flex items-center justify-center gap-8 text-sm mb-16 font-orbitron">
                    <div class="status-item">
                        <div class="status-dot online"></div>
                        <span>NEURAL NETWORK ONLINE</span>
                    </div>
                    <span class="text-cyber-blue">•</span>
                    <div class="status-item">
                        <div class="status-dot secure"></div>
                        <span>QUANTUM ENCRYPTION ACTIVE</span>
                    </div>
                </div>

                <!-- Neural Interface Preview -->
                <div class="relative max-w-5xl mx-auto">
                    <div class="neural-interface">
                        <div class="interface-header">
                            <div class="neural-controls">
                                <div class="control-node active"></div>
                                <div class="control-node standby"></div>
                                <div class="control-node offline"></div>
                            </div>
                            <div class="neural-toolbar">
                                <div class="toolbar-items">
                                    <i class="fas fa-brain text-cyber-blue neural-icon"></i>
                                    <i class="fas fa-microchip text-neon-pink neural-icon"></i>
                                    <i class="fas fa-atom text-matrix-green neural-icon"></i>
                                    <i class="fas fa-dna text-electric-blue neural-icon"></i>
                                    <i class="fas fa-network-wired text-cyber-purple neural-icon"></i>
                                    <i class="fas fa-satellite text-cyber-blue neural-icon"></i>
                                    <i class="fas fa-play text-matrix-green neural-icon active-tool"></i>
                                    <span class="ml-auto text-xs font-orbitron">SYNC: 100%</span>
                                    <span class="text-xs font-orbitron">EXPORT</span>
                                    <i class="fas fa-download text-cyber-blue neural-icon"></i>
                                </div>
                            </div>
                        </div>
                        <div class="interface-content">
                            <div class="neural-visualization">
                                <div class="central-core">
                                    <div class="quantum-sphere">
                                        <div class="sphere-core"></div>
                                        <div class="energy-rings">
                                            <div class="ring ring-1"></div>
                                            <div class="ring ring-2"></div>
                                            <div class="ring ring-3"></div>
                                        </div>
                                        <div class="quantum-particles-container">
                                            <div class="particle"></div>
                                            <div class="particle"></div>
                                            <div class="particle"></div>
                                            <div class="particle"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="neural-panel">
                                    <div class="panel-header font-orbitron">NEURAL MATRIX</div>
                                    <div class="neural-options">
                                        <div class="neural-item active">QUANTUM ENTANGLEMENT</div>
                                        <div class="neural-item">PHOTONIC RESONANCE</div>
                                        <div class="neural-item">PLASMA FIELD</div>
                                        <div class="neural-preview"></div>
                                        <div class="neural-stats">
                                            <div class="stat-line">
                                                <span>COHERENCE:</span>
                                                <div class="stat-bar">
                                                    <div class="stat-fill" style="width: 87%"></div>
                                                </div>
                                            </div>
                                            <div class="stat-line">
                                                <span>STABILITY:</span>
                                                <div class="stat-bar">
                                                    <div class="stat-fill" style="width: 94%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="sync-badge">
                                    <span class="font-orbitron">NEURAL SYNC ESTABLISHED</span>
                                    <div class="sync-indicator"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Neural Systems Section -->
    <section id="systems" class="py-20 bg-dark-nebula/50 relative">
        <div class="absolute inset-0 neural-bg-pattern"></div>
        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-orbitron font-bold mb-4 cyber-gradient">NEURAL SYSTEMS</h2>
                <p class="text-cyber-blue max-w-3xl mx-auto font-rajdhani text-lg">
                    Advanced quantum-neural interfaces designed for consciousness expansion and reality manipulation
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div class="neural-feature-card">
                    <div class="neural-feature-icon">
                        <i class="fas fa-brain"></i>
                        <div class="icon-pulse"></div>
                    </div>
                    <h3 class="text-xl font-orbitron font-semibold mb-3 text-cyber-blue">CONSCIOUSNESS MAPPING</h3>
                    <p class="text-gray-300 font-rajdhani">Direct neural pathway integration with quantum consciousness matrices for enhanced cognitive processing</p>
                    <div class="feature-stats">
                        <div class="stat">
                            <span class="stat-value">99.7%</span>
                            <span class="stat-label">ACCURACY</span>
                        </div>
                    </div>
                </div>

                <div class="neural-feature-card">
                    <div class="neural-feature-icon">
                        <i class="fas fa-atom"></i>
                        <div class="icon-pulse"></div>
                    </div>
                    <h3 class="text-xl font-orbitron font-semibold mb-3 text-neon-pink">QUANTUM RENDERING</h3>
                    <p class="text-gray-300 font-rajdhani">Subatomic particle manipulation for photorealistic quantum field visualization and matter reconstruction</p>
                    <div class="feature-stats">
                        <div class="stat">
                            <span class="stat-value">∞</span>
                            <span class="stat-label">RESOLUTION</span>
                        </div>
                    </div>
                </div>

                <div class="neural-feature-card">
                    <div class="neural-feature-icon">
                        <i class="fas fa-network-wired"></i>
                        <div class="icon-pulse"></div>
                    </div>
                    <h3 class="text-xl font-orbitron font-semibold mb-3 text-matrix-green">HIVE MIND PROTOCOL</h3>
                    <p class="text-gray-300 font-rajdhani">Collective consciousness networking with real-time thought synchronization and shared neural experiences</p>
                    <div class="feature-stats">
                        <div class="stat">
                            <span class="stat-value">∞</span>
                            <span class="stat-label">CONNECTIONS</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark-card py-12 border-t border-white/10">
        <div class="container mx-auto px-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <i class="fab fa-twitter text-gray-400 hover:text-white cursor-pointer"></i>
                    <i class="fab fa-discord text-gray-400 hover:text-white cursor-pointer"></i>
                    <i class="fab fa-github text-gray-400 hover:text-white cursor-pointer"></i>
                </div>
                <div class="text-sm text-gray-400">
                    All rights reserved © 2024
                </div>
                <div class="flex items-center space-x-6 text-sm text-gray-400">
                    <a href="#" class="hover:text-white">Privacy</a>
                    <a href="#" class="hover:text-white">Terms</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="modern-ui.js"></script>
</body>
</html>
