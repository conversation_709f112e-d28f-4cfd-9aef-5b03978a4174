<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VisionForge - The New Era of Visualization</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="modern-ui.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'purple-primary': '#8B5CF6',
                        'purple-secondary': '#A855F7',
                        'purple-dark': '#7C3AED',
                        'teal-accent': '#14B8A6',
                        'dark-bg': '#0F0F23',
                        'dark-card': '#1A1A2E',
                        'dark-surface': '#16213E',
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-dark-bg text-white overflow-x-hidden">
    <!-- Navigation -->
    <nav class="fixed w-full bg-dark-bg/80 backdrop-blur-lg z-50 border-b border-white/10">
        <div class="container mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-purple-primary to-teal-accent rounded-lg flex items-center justify-center">
                        <i class="fas fa-cube text-white text-sm"></i>
                    </div>
                    <span class="text-xl font-bold">VisionForge</span>
                    <span class="bg-teal-accent/20 text-teal-accent px-2 py-1 rounded text-xs font-medium">BETA</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#features" class="nav-link">Features</a>
                    <a href="#resources" class="nav-link">Resources</a>
                    <a href="#examples" class="nav-link">Examples</a>
                    <a href="#pricing" class="nav-link">Pricing</a>
                    <a href="#about" class="nav-link">About</a>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-300 hover:text-white transition-colors">Sign in</button>
                    <button class="bg-purple-primary hover:bg-purple-dark px-6 py-2 rounded-lg font-medium transition-colors">
                        Sign up
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center relative pt-20 overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-64 h-64 bg-purple-primary/20 rounded-full blur-3xl"></div>
            <div class="absolute bottom-20 right-10 w-96 h-96 bg-teal-accent/20 rounded-full blur-3xl"></div>
            <div class="grid-overlay"></div>
        </div>

        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center max-w-6xl mx-auto">
                <!-- Floating UI Elements -->
                <div class="floating-elements">
                    <div class="floating-card task-system">
                        <i class="fas fa-tasks"></i>
                        <span>task system</span>
                    </div>
                    <div class="floating-card join-us">
                        <span>Join to us!</span>
                    </div>
                    <div class="floating-card integrated-comments">
                        <span>integrated comments</span>
                    </div>
                </div>

                <!-- Main Heading -->
                <h1 class="text-6xl md:text-8xl font-black mb-8 leading-tight">
                    THE <span class="text-gradient">NEW ERA</span> OF 
                    <span class="text-gradient">VISUALISATION</span>
                </h1>

                <!-- Subheading -->
                <div class="mb-12">
                    <p class="text-xl text-gray-300 mb-2">EMPOWERING CREATORS WITH</p>
                    <p class="text-xl text-gray-300">POWERFUL TOOLS</p>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-16">
                    <button class="btn-primary">
                        Get Started
                    </button>
                    <button class="btn-secondary">
                        Request A Demo
                    </button>
                </div>

                <!-- Trust Indicators -->
                <div class="flex items-center justify-center gap-4 text-sm text-gray-400 mb-16">
                    <span>No credit card required</span>
                    <span>•</span>
                    <span>7-days free trial</span>
                </div>

                <!-- 3D Visualization Preview -->
                <div class="relative max-w-4xl mx-auto">
                    <div class="browser-mockup">
                        <div class="browser-header">
                            <div class="browser-controls">
                                <div class="control-btn bg-red-500"></div>
                                <div class="control-btn bg-yellow-500"></div>
                                <div class="control-btn bg-green-500"></div>
                            </div>
                            <div class="browser-toolbar">
                                <div class="toolbar-items">
                                    <i class="fas fa-cube text-teal-accent"></i>
                                    <i class="fas fa-square"></i>
                                    <i class="fas fa-circle"></i>
                                    <i class="fas fa-triangle"></i>
                                    <i class="fas fa-hexagon"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-play text-yellow-400"></i>
                                    <span class="ml-auto text-xs">100%</span>
                                    <span class="text-xs">Export</span>
                                    <i class="fas fa-download text-teal-accent"></i>
                                </div>
                            </div>
                        </div>
                        <div class="browser-content">
                            <div class="visualization-container">
                                <div class="main-object">
                                    <div class="torus-shape"></div>
                                </div>
                                <div class="side-panel">
                                    <div class="panel-header">Texture Material</div>
                                    <div class="material-options">
                                        <div class="material-item active">Metallic Chrome</div>
                                        <div class="material-preview"></div>
                                    </div>
                                </div>
                                <div class="create-together-badge">
                                    <span>Let's create together!</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-dark-surface/50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">Powerful Features</h2>
                <p class="text-gray-400 max-w-2xl mx-auto">
                    Everything you need to create stunning visualizations and bring your ideas to life
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">3D Modeling</h3>
                    <p class="text-gray-400">Create complex 3D models with our intuitive tools and real-time rendering</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Material Editor</h3>
                    <p class="text-gray-400">Advanced material system with realistic textures and lighting</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Collaboration</h3>
                    <p class="text-gray-400">Work together in real-time with integrated comments and task management</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark-card py-12 border-t border-white/10">
        <div class="container mx-auto px-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <i class="fab fa-twitter text-gray-400 hover:text-white cursor-pointer"></i>
                    <i class="fab fa-discord text-gray-400 hover:text-white cursor-pointer"></i>
                    <i class="fab fa-github text-gray-400 hover:text-white cursor-pointer"></i>
                </div>
                <div class="text-sm text-gray-400">
                    All rights reserved © 2024
                </div>
                <div class="flex items-center space-x-6 text-sm text-gray-400">
                    <a href="#" class="hover:text-white">Privacy</a>
                    <a href="#" class="hover:text-white">Terms</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="modern-ui.js"></script>
</body>
</html>
