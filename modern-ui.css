/* Modern UI CSS - Inspired by VisionForge Design */

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #0F0F23 0%, #1A1A2E 50%, #16213E 100%);
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
}

/* Navigation Styles */
.nav-link {
    color: #9CA3AF;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
}

.nav-link:hover {
    color: #FFFFFF;
    background: rgba(139, 92, 246, 0.1);
}

/* Grid Overlay */
.grid-overlay {
    position: absolute;
    inset: 0;
    background-image: 
        linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
}

/* Text Gradient */
.text-gradient {
    background: linear-gradient(135deg, #8B5CF6, #14B8A6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    inset: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: rgba(26, 26, 46, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    animation: float 6s ease-in-out infinite;
}

.task-system {
    top: 20%;
    left: 10%;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #14B8A6;
    animation-delay: 0s;
}

.join-us {
    top: 30%;
    right: 15%;
    background: linear-gradient(135deg, #14B8A6, #0D9488);
    color: white;
    animation-delay: 2s;
}

.integrated-comments {
    bottom: 30%;
    right: 20%;
    color: #8B5CF6;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(139, 92, 246, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    border-color: #8B5CF6;
    background: rgba(139, 92, 246, 0.1);
    transform: translateY(-2px);
}

/* Browser Mockup */
.browser-mockup {
    background: rgba(26, 26, 46, 0.8);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.browser-header {
    background: rgba(15, 15, 35, 0.9);
    padding: 12px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.browser-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.control-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.browser-toolbar {
    display: flex;
    align-items: center;
    gap: 16px;
}

.toolbar-items {
    display: flex;
    align-items: center;
    gap: 16px;
    width: 100%;
    color: #9CA3AF;
    font-size: 14px;
}

.browser-content {
    padding: 40px;
    min-height: 400px;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 46, 0.9) 100%);
}

/* Visualization Container */
.visualization-container {
    position: relative;
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-object {
    position: relative;
    z-index: 2;
}

.torus-shape {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, #14B8A6, #0D9488);
    border-radius: 50%;
    position: relative;
    animation: rotate3d 8s linear infinite;
    box-shadow: 
        0 0 60px rgba(20, 184, 166, 0.4),
        inset 0 0 60px rgba(20, 184, 166, 0.2);
}

.torus-shape::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    background: #0F0F23;
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

@keyframes rotate3d {
    0% { transform: rotateY(0deg) rotateX(15deg); }
    100% { transform: rotateY(360deg) rotateX(15deg); }
}

.side-panel {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(26, 26, 46, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    width: 180px;
    backdrop-filter: blur(10px);
}

.panel-header {
    font-size: 14px;
    font-weight: 600;
    color: white;
    margin-bottom: 12px;
}

.material-item {
    font-size: 12px;
    color: #9CA3AF;
    margin-bottom: 8px;
}

.material-item.active {
    color: #14B8A6;
}

.material-preview {
    width: 100%;
    height: 60px;
    background: linear-gradient(135deg, #14B8A6, #0D9488);
    border-radius: 8px;
    margin-top: 8px;
}

.create-together-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Feature Cards */
.feature-card {
    background: rgba(26, 26, 46, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 32px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.feature-card:hover {
    transform: translateY(-8px);
    border-color: #8B5CF6;
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

.feature-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #8B5CF6, #14B8A6);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 24px;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .floating-card {
        display: none;
    }
    
    h1 {
        font-size: 3rem !important;
    }
    
    .browser-content {
        padding: 20px;
    }
    
    .visualization-container {
        height: 250px;
    }
    
    .torus-shape {
        width: 150px;
        height: 150px;
    }
    
    .side-panel {
        position: relative;
        left: 0;
        top: 0;
        transform: none;
        margin-top: 20px;
        width: 100%;
    }
}
