/* Sci-Fi Neural Interface CSS */

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: radial-gradient(ellipse at center, #000011 0%, #0A0A1A 50%, #1A0A2E 100%);
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    overflow-x: hidden;
    position: relative;
}

/* Sci-Fi Background Effects */
.stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(2px 2px at 20px 30px, #00FFFF, transparent),
        radial-gradient(2px 2px at 40px 70px, #FF00FF, transparent),
        radial-gradient(1px 1px at 90px 40px, #00FF41, transparent),
        radial-gradient(1px 1px at 130px 80px, #FF0080, transparent),
        radial-gradient(2px 2px at 160px 30px, #0080FF, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: stars-move 20s linear infinite;
    opacity: 0.6;
}

@keyframes stars-move {
    from { transform: translateY(0px); }
    to { transform: translateY(-100px); }
}

.nebula {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse at 20% 50%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at 80% 20%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at 40% 80%, rgba(0, 255, 65, 0.1) 0%, transparent 50%);
    animation: nebula-drift 30s ease-in-out infinite;
}

@keyframes nebula-drift {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

.scanning-lines {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 255, 0.03) 2px,
        rgba(0, 255, 255, 0.03) 4px
    );
    animation: scan 2s linear infinite;
    pointer-events: none;
}

@keyframes scan {
    0% { transform: translateY(-100%); }
    100% { transform: translateY(100vh); }
}

/* Navigation Styles */
.nav-glow {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.nav-link-sci {
    color: #00FFFF;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 8px;
    position: relative;
    letter-spacing: 1px;
}

.nav-link-sci:hover {
    color: #FFFFFF;
    background: rgba(0, 255, 255, 0.1);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.nav-link-sci::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.nav-link-sci:hover::before {
    opacity: 1;
}

/* Cyber Text Effects */
.cyber-text {
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
    animation: cyber-flicker 2s ease-in-out infinite alternate;
}

@keyframes cyber-flicker {
    0%, 100% { text-shadow: 0 0 10px rgba(0, 255, 255, 0.8); }
    50% { text-shadow: 0 0 20px rgba(0, 255, 255, 1), 0 0 30px rgba(0, 255, 255, 0.8); }
}

.glow-text {
    text-shadow: 0 0 5px rgba(0, 255, 65, 0.8);
}

.glow-button {
    box-shadow: 0 0 20px rgba(255, 0, 128, 0.5);
    transition: all 0.3s ease;
}

.glow-button:hover {
    box-shadow: 0 0 30px rgba(255, 0, 128, 0.8), 0 0 40px rgba(128, 0, 255, 0.6);
}

.neural-glow {
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
    animation: neural-pulse 2s ease-in-out infinite;
}

@keyframes neural-pulse {
    0%, 100% { box-shadow: 0 0 15px rgba(0, 255, 255, 0.6); }
    50% { box-shadow: 0 0 25px rgba(0, 255, 255, 0.9), 0 0 35px rgba(255, 0, 255, 0.5); }
}

.pulse-glow {
    animation: pulse-glow 1.5s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% { color: #FFFFFF; }
    50% { color: #00FFFF; text-shadow: 0 0 15px rgba(0, 255, 255, 1); }
}

/* Cyber Grid */
.cyber-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.4;
    animation: grid-pulse 4s ease-in-out infinite;
}

@keyframes grid-pulse {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 0.6; }
}

.data-streams {
    position: absolute;
    inset: 0;
    background:
        linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent),
        linear-gradient(0deg, transparent, rgba(255, 0, 255, 0.1), transparent);
    background-size: 200px 200px;
    animation: data-flow 8s linear infinite;
}

@keyframes data-flow {
    0% { background-position: 0% 0%; }
    100% { background-position: 100% 100%; }
}

/* Cyber Text Gradients */
.cyber-gradient {
    background: linear-gradient(135deg, #00FFFF, #FF00FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

.cyber-gradient-alt {
    background: linear-gradient(135deg, #00FF41, #0080FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift-alt 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(90deg); }
}

@keyframes gradient-shift-alt {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(-90deg); }
}

.cyber-title {
    text-shadow:
        0 0 10px rgba(0, 255, 255, 0.8),
        0 0 20px rgba(0, 255, 255, 0.6),
        0 0 30px rgba(0, 255, 255, 0.4);
    animation: title-glow 2s ease-in-out infinite alternate;
}

@keyframes title-glow {
    0% {
        text-shadow:
            0 0 10px rgba(0, 255, 255, 0.8),
            0 0 20px rgba(0, 255, 255, 0.6),
            0 0 30px rgba(0, 255, 255, 0.4);
    }
    100% {
        text-shadow:
            0 0 15px rgba(0, 255, 255, 1),
            0 0 25px rgba(0, 255, 255, 0.8),
            0 0 35px rgba(0, 255, 255, 0.6);
    }
}

/* Sci-Fi Floating Elements */
.floating-elements {
    position: absolute;
    inset: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: rgba(0, 0, 17, 0.9);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 12px;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
    animation: neural-float 6s ease-in-out infinite;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.neural-link {
    top: 15%;
    left: 8%;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #00FFFF;
    animation-delay: 0s;
    border-color: rgba(0, 255, 255, 0.5);
}

.quantum-core {
    top: 25%;
    right: 12%;
    background: linear-gradient(135deg, rgba(255, 0, 255, 0.2), rgba(128, 0, 255, 0.2));
    color: #FF00FF;
    border-color: rgba(255, 0, 255, 0.5);
    animation-delay: 1.5s;
}

.bio-sync {
    bottom: 35%;
    left: 15%;
    color: #00FF41;
    border-color: rgba(0, 255, 65, 0.5);
    animation-delay: 3s;
}

.data-stream {
    bottom: 20%;
    right: 18%;
    color: #0080FF;
    border-color: rgba(0, 128, 255, 0.5);
    animation-delay: 4.5s;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-indicator.active {
    background: #00FF41;
    box-shadow: 0 0 10px #00FF41;
    animation: status-pulse 1s ease-in-out infinite;
}

@keyframes status-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.quantum-particles {
    display: flex;
    gap: 4px;
}

.quantum-particles::before,
.quantum-particles::after {
    content: '';
    width: 4px;
    height: 4px;
    background: #FF00FF;
    border-radius: 50%;
    animation: quantum-spin 2s linear infinite;
}

@keyframes quantum-spin {
    0% { transform: rotate(0deg) translateX(10px) rotate(0deg); }
    100% { transform: rotate(360deg) translateX(10px) rotate(-360deg); }
}

.sync-waves::before {
    content: '';
    position: absolute;
    right: -20px;
    top: 50%;
    width: 15px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00FF41, transparent);
    animation: wave-pulse 1.5s ease-in-out infinite;
}

@keyframes wave-pulse {
    0%, 100% { transform: translateY(-50%) scaleX(0.5); opacity: 0.5; }
    50% { transform: translateY(-50%) scaleX(1.5); opacity: 1; }
}

.stream-lines {
    position: absolute;
    right: -25px;
    top: 0;
    bottom: 0;
    width: 20px;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        #0080FF 2px,
        #0080FF 4px
    );
    animation: stream-flow 1s linear infinite;
}

@keyframes stream-flow {
    0% { transform: translateY(0); }
    100% { transform: translateY(8px); }
}

@keyframes neural-float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-15px) rotate(1deg); }
    50% { transform: translateY(-25px) rotate(0deg); }
    75% { transform: translateY(-15px) rotate(-1deg); }
}

/* Sci-Fi Buttons */
.btn-neural {
    background: linear-gradient(135deg, #00FFFF, #0080FF);
    color: #000011;
    padding: 18px 36px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 16px;
    font-family: 'Orbitron', monospace;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.5),
        0 8px 32px rgba(0, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-neural:hover {
    transform: translateY(-3px);
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.8),
        0 12px 40px rgba(0, 255, 255, 0.4);
    background: linear-gradient(135deg, #00FFFF, #00FF41);
}

.btn-quantum {
    background: transparent;
    color: #FF00FF;
    padding: 18px 36px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 16px;
    font-family: 'Orbitron', monospace;
    border: 2px solid #FF00FF;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.3);
}

.btn-quantum:hover {
    background: rgba(255, 0, 255, 0.1);
    transform: translateY(-3px);
    box-shadow:
        0 0 25px rgba(255, 0, 255, 0.6),
        0 8px 32px rgba(255, 0, 255, 0.3);
    color: #FFFFFF;
}

.neural-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
    transform: translate(-50%, -50%) scale(0);
    animation: neural-pulse-anim 2s ease-in-out infinite;
}

@keyframes neural-pulse-anim {
    0%, 100% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
    50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

.quantum-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 0, 255, 0.4), transparent);
    animation: quantum-sweep 3s ease-in-out infinite;
}

@keyframes quantum-sweep {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

/* Neural Interface */
.neural-interface {
    background: rgba(0, 0, 17, 0.9);
    border-radius: 20px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    backdrop-filter: blur(25px);
    overflow: hidden;
    box-shadow:
        0 0 40px rgba(0, 255, 255, 0.3),
        0 20px 60px rgba(0, 0, 0, 0.5);
    animation: interface-glow 3s ease-in-out infinite alternate;
}

@keyframes interface-glow {
    0% { box-shadow: 0 0 40px rgba(0, 255, 255, 0.3), 0 20px 60px rgba(0, 0, 0, 0.5); }
    100% { box-shadow: 0 0 60px rgba(0, 255, 255, 0.5), 0 20px 80px rgba(0, 0, 0, 0.7); }
}

.interface-header {
    background: rgba(0, 0, 17, 0.95);
    padding: 16px 20px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.neural-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.control-node {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid;
}

.control-node.active {
    background: #00FF41;
    border-color: #00FF41;
    box-shadow: 0 0 15px #00FF41;
    animation: node-pulse 1.5s ease-in-out infinite;
}

.control-node.standby {
    background: #FFFF00;
    border-color: #FFFF00;
    box-shadow: 0 0 10px #FFFF00;
}

.control-node.offline {
    background: #FF0000;
    border-color: #FF0000;
    box-shadow: 0 0 8px #FF0000;
}

@keyframes node-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.neural-toolbar {
    display: flex;
    align-items: center;
    gap: 20px;
}

.neural-icon {
    font-size: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.neural-icon:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 0 8px currentColor);
}

.neural-icon.active-tool {
    animation: tool-active 1s ease-in-out infinite;
}

@keyframes tool-active {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); filter: drop-shadow(0 0 12px currentColor); }
}

.interface-content {
    padding: 40px;
    min-height: 450px;
    background:
        radial-gradient(ellipse at center, rgba(0, 255, 255, 0.05) 0%, transparent 50%),
        linear-gradient(135deg, rgba(0, 0, 17, 0.95) 0%, rgba(26, 10, 46, 0.95) 100%);
}

.neural-visualization {
    position: relative;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.central-core {
    position: relative;
    z-index: 3;
}

.quantum-sphere {
    width: 250px;
    height: 250px;
    position: relative;
    animation: sphere-rotate 12s linear infinite;
}

@keyframes sphere-rotate {
    0% { transform: rotateY(0deg) rotateX(15deg); }
    100% { transform: rotateY(360deg) rotateX(15deg); }
}

.sphere-core {
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 30%, rgba(0, 255, 255, 0.8), rgba(0, 128, 255, 0.6)),
        radial-gradient(circle at 70% 70%, rgba(255, 0, 255, 0.6), rgba(128, 0, 255, 0.4));
    border-radius: 50%;
    position: relative;
    box-shadow:
        0 0 60px rgba(0, 255, 255, 0.6),
        inset 0 0 60px rgba(0, 255, 255, 0.3);
    animation: core-pulse 3s ease-in-out infinite;
}

@keyframes core-pulse {
    0%, 100% {
        box-shadow:
            0 0 60px rgba(0, 255, 255, 0.6),
            inset 0 0 60px rgba(0, 255, 255, 0.3);
    }
    50% {
        box-shadow:
            0 0 80px rgba(0, 255, 255, 0.8),
            inset 0 0 80px rgba(0, 255, 255, 0.5);
    }
}

.energy-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.ring {
    position: absolute;
    border: 2px solid;
    border-radius: 50%;
    animation: ring-spin 8s linear infinite;
}

.ring-1 {
    width: 300px;
    height: 300px;
    border-color: rgba(0, 255, 255, 0.6);
    top: -25px;
    left: -25px;
    animation-duration: 8s;
}

.ring-2 {
    width: 350px;
    height: 350px;
    border-color: rgba(255, 0, 255, 0.4);
    top: -50px;
    left: -50px;
    animation-duration: 12s;
    animation-direction: reverse;
}

.ring-3 {
    width: 400px;
    height: 400px;
    border-color: rgba(0, 255, 65, 0.3);
    top: -75px;
    left: -75px;
    animation-duration: 16s;
}

@keyframes ring-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.quantum-particles-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #00FFFF;
    border-radius: 50%;
    box-shadow: 0 0 10px #00FFFF;
}

.particle:nth-child(1) {
    top: -100px;
    left: 0;
    animation: particle-orbit-1 4s linear infinite;
}

.particle:nth-child(2) {
    top: 0;
    left: 100px;
    animation: particle-orbit-2 4s linear infinite;
    animation-delay: 1s;
}

.particle:nth-child(3) {
    top: 100px;
    left: 0;
    animation: particle-orbit-3 4s linear infinite;
    animation-delay: 2s;
}

.particle:nth-child(4) {
    top: 0;
    left: -100px;
    animation: particle-orbit-4 4s linear infinite;
    animation-delay: 3s;
}

@keyframes particle-orbit-1 {
    0% { transform: rotate(0deg) translateX(100px) rotate(0deg); }
    100% { transform: rotate(360deg) translateX(100px) rotate(-360deg); }
}

@keyframes particle-orbit-2 {
    0% { transform: rotate(90deg) translateX(100px) rotate(-90deg); }
    100% { transform: rotate(450deg) translateX(100px) rotate(-450deg); }
}

@keyframes particle-orbit-3 {
    0% { transform: rotate(180deg) translateX(100px) rotate(-180deg); }
    100% { transform: rotate(540deg) translateX(100px) rotate(-540deg); }
}

@keyframes particle-orbit-4 {
    0% { transform: rotate(270deg) translateX(100px) rotate(-270deg); }
    100% { transform: rotate(630deg) translateX(100px) rotate(-630deg); }
}

/* Neural Panel */
.neural-panel {
    position: absolute;
    left: 30px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 17, 0.95);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    width: 220px;
    backdrop-filter: blur(15px);
    box-shadow: 0 0 25px rgba(0, 255, 255, 0.2);
}

.panel-header {
    font-size: 14px;
    font-weight: 700;
    color: #00FFFF;
    margin-bottom: 16px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.neural-item {
    font-size: 12px;
    color: #9CA3AF;
    margin-bottom: 8px;
    padding: 6px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.neural-item.active {
    color: #00FFFF;
    background: rgba(0, 255, 255, 0.1);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.neural-item:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
}

.neural-preview {
    width: 100%;
    height: 80px;
    background: linear-gradient(135deg, #00FFFF, #0080FF);
    border-radius: 12px;
    margin: 16px 0;
    position: relative;
    overflow: hidden;
    animation: preview-shimmer 3s ease-in-out infinite;
}

@keyframes preview-shimmer {
    0%, 100% { background: linear-gradient(135deg, #00FFFF, #0080FF); }
    33% { background: linear-gradient(135deg, #FF00FF, #8000FF); }
    66% { background: linear-gradient(135deg, #00FF41, #00AA2A); }
}

.neural-stats {
    margin-top: 16px;
}

.stat-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 10px;
    color: #9CA3AF;
}

.stat-bar {
    width: 60px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.stat-fill {
    height: 100%;
    background: linear-gradient(90deg, #00FFFF, #00FF41);
    border-radius: 2px;
    animation: stat-glow 2s ease-in-out infinite;
}

@keyframes stat-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(0, 255, 255, 0.5); }
    50% { box-shadow: 0 0 10px rgba(0, 255, 255, 0.8); }
}

.sync-badge {
    position: absolute;
    bottom: 30px;
    right: 30px;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 128, 255, 0.2));
    border: 1px solid rgba(0, 255, 255, 0.5);
    color: #00FFFF;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: badge-pulse 2s ease-in-out infinite;
}

@keyframes badge-pulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 15px rgba(0, 255, 255, 0.3); }
    50% { transform: scale(1.05); box-shadow: 0 0 25px rgba(0, 255, 255, 0.5); }
}

.sync-indicator {
    width: 8px;
    height: 8px;
    background: #00FF41;
    border-radius: 50%;
    animation: sync-blink 1s ease-in-out infinite;
}

@keyframes sync-blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* Neural Feature Cards */
.neural-feature-card {
    background: rgba(0, 0, 17, 0.8);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    transition: all 0.4s ease;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.neural-feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.neural-feature-card:hover::before {
    left: 100%;
}

.neural-feature-card:hover {
    transform: translateY(-10px);
    border-color: #00FFFF;
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.4),
        0 20px 40px rgba(0, 0, 0, 0.3);
}

.neural-feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 128, 255, 0.2));
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 32px;
    color: #00FFFF;
    position: relative;
    overflow: hidden;
}

.icon-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(0, 255, 255, 0.3) 0%, transparent 70%);
    transform: translate(-50%, -50%) scale(0);
    animation: icon-pulse-anim 2s ease-in-out infinite;
}

@keyframes icon-pulse-anim {
    0%, 100% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
    50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
}

.feature-stats {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 255, 255, 0.2);
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
    color: #00FFFF;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.stat-label {
    font-size: 10px;
    color: #9CA3AF;
    font-family: 'Orbitron', monospace;
    letter-spacing: 1px;
    margin-top: 4px;
}

/* Background Patterns */
.neural-bg-pattern {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
        linear-gradient(45deg, transparent 40%, rgba(0, 255, 65, 0.05) 50%, transparent 60%);
    animation: pattern-shift 10s ease-in-out infinite;
}

@keyframes pattern-shift {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.8; }
}

/* Pulse Orbs */
.pulse-orb {
    animation: pulse-orb-anim 4s ease-in-out infinite;
}

.pulse-orb-delayed {
    animation: pulse-orb-anim 4s ease-in-out infinite;
    animation-delay: 2s;
}

.pulse-orb-slow {
    animation: pulse-orb-anim 6s ease-in-out infinite;
    animation-delay: 1s;
}

@keyframes pulse-orb-anim {
    0%, 100% { transform: scale(1); opacity: 0.6; }
    50% { transform: scale(1.2); opacity: 0.9; }
}

/* Status Items */
.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #9CA3AF;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: status-blink 2s ease-in-out infinite;
}

.status-dot.online {
    background: #00FF41;
    box-shadow: 0 0 10px #00FF41;
}

.status-dot.secure {
    background: #00FFFF;
    box-shadow: 0 0 10px #00FFFF;
}

@keyframes status-blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .floating-card {
        display: none;
    }

    .cyber-title {
        font-size: 3rem !important;
    }

    .interface-content {
        padding: 20px;
    }

    .neural-visualization {
        height: 300px;
    }

    .quantum-sphere {
        width: 180px;
        height: 180px;
    }

    .neural-panel {
        position: relative;
        left: 0;
        top: 0;
        transform: none;
        margin-top: 20px;
        width: 100%;
    }

    .ring-1, .ring-2, .ring-3 {
        display: none;
    }
}

.browser-header {
    background: rgba(15, 15, 35, 0.9);
    padding: 12px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.browser-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.control-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.browser-toolbar {
    display: flex;
    align-items: center;
    gap: 16px;
}

.toolbar-items {
    display: flex;
    align-items: center;
    gap: 16px;
    width: 100%;
    color: #9CA3AF;
    font-size: 14px;
}

.browser-content {
    padding: 40px;
    min-height: 400px;
    background: linear-gradient(135deg, rgba(15, 15, 35, 0.9) 0%, rgba(26, 26, 46, 0.9) 100%);
}

/* Visualization Container */
.visualization-container {
    position: relative;
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-object {
    position: relative;
    z-index: 2;
}

.torus-shape {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, #14B8A6, #0D9488);
    border-radius: 50%;
    position: relative;
    animation: rotate3d 8s linear infinite;
    box-shadow: 
        0 0 60px rgba(20, 184, 166, 0.4),
        inset 0 0 60px rgba(20, 184, 166, 0.2);
}

.torus-shape::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    background: #0F0F23;
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

@keyframes rotate3d {
    0% { transform: rotateY(0deg) rotateX(15deg); }
    100% { transform: rotateY(360deg) rotateX(15deg); }
}

.side-panel {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(26, 26, 46, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    width: 180px;
    backdrop-filter: blur(10px);
}

.panel-header {
    font-size: 14px;
    font-weight: 600;
    color: white;
    margin-bottom: 12px;
}

.material-item {
    font-size: 12px;
    color: #9CA3AF;
    margin-bottom: 8px;
}

.material-item.active {
    color: #14B8A6;
}

.material-preview {
    width: 100%;
    height: 60px;
    background: linear-gradient(135deg, #14B8A6, #0D9488);
    border-radius: 8px;
    margin-top: 8px;
}

.create-together-badge {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Feature Cards */
.feature-card {
    background: rgba(26, 26, 46, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 32px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.feature-card:hover {
    transform: translateY(-8px);
    border-color: #8B5CF6;
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

.feature-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, #8B5CF6, #14B8A6);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 24px;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .floating-card {
        display: none;
    }
    
    h1 {
        font-size: 3rem !important;
    }
    
    .browser-content {
        padding: 20px;
    }
    
    .visualization-container {
        height: 250px;
    }
    
    .torus-shape {
        width: 150px;
        height: 150px;
    }
    
    .side-panel {
        position: relative;
        left: 0;
        top: 0;
        transform: none;
        margin-top: 20px;
        width: 100%;
    }
}
