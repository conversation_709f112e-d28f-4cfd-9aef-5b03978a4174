<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Assistant - Your Intelligent Programming Partner</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#000000',
                        secondary: '#1a1a1a',
                        accent: '#ffffff',
                        'accent-secondary': '#f8f9fa',
                        dark: '#000000',
                        'dark-secondary': '#111111',
                        'light-gray': '#f3f4f6',
                        'medium-gray': '#6b7280',
                        'dark-gray': '#374151',
                        'neon-pink': '#ff0080',
                        'neon-blue': '#00ffff',
                        'neon-purple': '#8000ff',
                        'hot-red': '#ff1744',
                        'sexy-gold': '#ffd700'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'serif': ['Playfair Display', 'serif'],
                        'mono': ['JetBrains Mono', 'monospace']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-dark via-dark-secondary to-secondary text-white font-sans">
    <!-- Navigation -->
    <nav class="navbar-modern fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="flex items-center space-x-3">
                    <div class="navbar-logo w-10 h-10 rounded-xl flex items-center justify-center">
                        <i class="fas fa-code text-white text-lg"></i>
                    </div>
                    <span class="navbar-brand text-2xl font-bold">
                        AI Code Assistant
                    </span>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="nav-link-modern">Features</a>
                    <a href="#stats" class="nav-link-modern">Stats</a>
                    <a href="#pricing" class="nav-link-modern">Pricing</a>
                    <a href="#testimonials" class="nav-link-modern">Reviews</a>
                    <a href="#about" class="nav-link-modern">About</a>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="btn-secondary-modern">
                        Sign In
                    </button>
                    <button class="btn-primary-modern">
                        Get Started
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center relative overflow-hidden hero-bg">
        <div class="absolute inset-0 bg-gradient-to-r from-primary/10 to-accent/10"></div>
        
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="animate-fade-in">
                <h1 class="hero-title text-5xl md:text-7xl font-bold mb-6">
                    <span class="gradient-text" data-text="Code Smarter">
                        Code Smarter
                    </span>
                    <br>
                    <span class="text-4xl md:text-6xl text-gray-300">
                        with AI
                    </span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Experience the future of programming with our intelligent AI assistant. 
                    Write, debug, and optimize code faster than ever before with cutting-edge AI technology.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                    <button class="btn-primary-modern text-lg px-8 py-4 animate-sexy-bounce">
                        <i class="fas fa-rocket mr-2"></i>
                        Start Coding Now
                    </button>
                    <button class="btn-secondary-modern text-lg px-8 py-4">
                        <i class="fas fa-play mr-2"></i>
                        Watch Demo
                    </button>
                </div>
                
                <!-- Technology Stack -->
                <div class="tech-stack">
                    <span class="tech-item">JavaScript</span>
                    <span class="tech-item">Python</span>
                    <span class="tech-item">React</span>
                    <span class="tech-item">Node.js</span>
                    <span class="tech-item">TypeScript</span>
                    <span class="tech-item">Go</span>
                    <span class="tech-item">Rust</span>
                    <span class="tech-item">Java</span>
                </div>
            </div>
        </div>

        <!-- Floating Elements -->
        <div class="floating-element absolute top-20 left-10 w-20 h-20 rounded-full blur-xl animate-pulse"></div>
        <div class="floating-element absolute bottom-20 right-10 w-32 h-32 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div class="floating-element absolute top-1/2 left-1/4 w-16 h-16 rounded-full blur-lg animate-bounce"></div>
    </section>

    <!-- Stats Section -->
    <section id="stats" class="py-20 relative bg-black/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title text-4xl md:text-5xl font-bold mb-6">
                    <span class="gradient-text" data-text="Trusted by Developers Worldwide">
                        Trusted by Developers Worldwide
                    </span>
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Join thousands of developers who have transformed their coding experience
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="stats-card">
                    <div class="stats-number" data-value="50K+">50K+</div>
                    <p class="text-gray-300 font-medium">Active Developers</p>
                </div>
                <div class="stats-card">
                    <div class="stats-number" data-value="1M+">1M+</div>
                    <p class="text-gray-300 font-medium">Lines of Code Generated</p>
                </div>
                <div class="stats-card">
                    <div class="stats-number" data-value="99.9%">99.9%</div>
                    <p class="text-gray-300 font-medium">Uptime</p>
                </div>
                <div class="stats-card">
                    <div class="stats-number" data-value="4.9/5">4.9/5</div>
                    <p class="text-gray-300 font-medium">User Rating</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title text-4xl md:text-5xl font-bold mb-6">
                    <span class="gradient-text" data-text="Powerful Features">
                        Powerful Features
                    </span>
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Everything you need to supercharge your development workflow
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="feature-card glass-royal rounded-xl p-8 hover:border-accent/40 transition-all duration-300">
                    <div class="icon-container w-16 h-16 bg-gradient-to-r from-accent to-light-gray rounded-xl flex items-center justify-center mb-6 animate-sexy-pulse">
                        <i class="fas fa-brain text-2xl text-dark"></i>
                    </div>
                    <h3 class="feature-title text-2xl font-bold mb-4 text-white">Intelligent Code Completion</h3>
                    <p class="text-gray-300 leading-relaxed">
                        Advanced AI that understands your codebase and provides context-aware suggestions in real-time with 95% accuracy.
                    </p>
                </div>

                <!-- Feature 2 -->
                <div class="feature-card glass-royal rounded-xl p-8 hover:border-accent/40 transition-all duration-300">
                    <div class="icon-container w-16 h-16 bg-gradient-to-r from-light-gray to-medium-gray rounded-xl flex items-center justify-center mb-6 animate-sexy-pulse">
                        <i class="fas fa-bug text-2xl text-dark"></i>
                    </div>
                    <h3 class="feature-title text-2xl font-bold mb-4 text-white">Smart Debugging</h3>
                    <p class="text-gray-300 leading-relaxed">
                        Automatically detect and fix bugs with intelligent error analysis and solution suggestions. Reduce debugging time by 70%.
                    </p>
                </div>

                <!-- Feature 3 -->
                <div class="feature-card glass-royal rounded-xl p-8 hover:border-accent/40 transition-all duration-300">
                    <div class="icon-container w-16 h-16 bg-gradient-to-r from-medium-gray to-dark-gray rounded-xl flex items-center justify-center mb-6 animate-sexy-pulse">
                        <i class="fas fa-code text-2xl text-white"></i>
                    </div>
                    <h3 class="feature-title text-2xl font-bold mb-4 text-white">Code Generation</h3>
                    <p class="text-gray-300 leading-relaxed">
                        Generate entire functions, classes, and modules from natural language descriptions with multiple language support.
                    </p>
                </div>

                <!-- Feature 4 -->
                <div class="feature-card glass-royal rounded-xl p-8 hover:border-accent/40 transition-all duration-300">
                    <div class="icon-container w-16 h-16 bg-gradient-to-r from-dark-gray to-primary rounded-xl flex items-center justify-center mb-6 animate-sexy-pulse">
                        <i class="fas fa-refresh text-2xl text-white"></i>
                    </div>
                    <h3 class="feature-title text-2xl font-bold mb-4 text-white">Code Refactoring</h3>
                    <p class="text-gray-300 leading-relaxed">
                        Automatically refactor code for better performance, readability, and maintainability with industry best practices.
                    </p>
                </div>

                <!-- Feature 5 -->
                <div class="feature-card glass-royal rounded-xl p-8 hover:border-accent/40 transition-all duration-300">
                    <div class="icon-container w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center mb-6 animate-sexy-pulse">
                        <i class="fas fa-comments text-2xl text-white"></i>
                    </div>
                    <h3 class="feature-title text-2xl font-bold mb-4 text-white">Natural Language Chat</h3>
                    <p class="text-gray-300 leading-relaxed">
                        Chat with AI about your code, ask questions, and get instant explanations with detailed documentation references.
                    </p>
                </div>

                <!-- Feature 6 -->
                <div class="feature-card glass-royal rounded-xl p-8 hover:border-accent/40 transition-all duration-300">
                    <div class="icon-container w-16 h-16 bg-gradient-to-r from-secondary to-accent rounded-xl flex items-center justify-center mb-6 animate-sexy-pulse">
                        <i class="fas fa-rocket text-2xl text-dark"></i>
                    </div>
                    <h3 class="feature-title text-2xl font-bold mb-4 text-white">Performance Optimization</h3>
                    <p class="text-gray-300 leading-relaxed">
                        Get suggestions for optimizing your code performance and reducing execution time with detailed analysis reports.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 relative bg-black/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title text-4xl md:text-5xl font-bold mb-6">
                    <span class="gradient-text" data-text="What Developers Say">
                        What Developers Say
                    </span>
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Real feedback from developers who use AI Code Assistant daily
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="testimonial-card">
                    <div class="testimonial-avatar">
                        <i class="fas fa-user text-dark text-xl"></i>
                    </div>
                    <p class="text-gray-300 mb-4 italic">
                        "AI Code Assistant has completely transformed my development workflow. The code completion is incredibly accurate and saves me hours every day."
                    </p>
                    <div class="flex items-center">
                        <div>
                            <h4 class="testimonial-name text-white font-semibold">Sarah Chen</h4>
                            <p class="text-gray-400 text-sm">Senior Full-Stack Developer</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-avatar">
                        <i class="fas fa-user text-dark text-xl"></i>
                    </div>
                    <p class="text-gray-300 mb-4 italic">
                        "The debugging features are game-changing. It catches issues I would have missed and provides clear solutions. Highly recommended!"
                    </p>
                    <div class="flex items-center">
                        <div>
                            <h4 class="testimonial-name text-white font-semibold">Marcus Rodriguez</h4>
                            <p class="text-gray-400 text-sm">Lead Backend Engineer</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-avatar">
                        <i class="fas fa-user text-dark text-xl"></i>
                    </div>
                    <p class="text-gray-300 mb-4 italic">
                        "As a startup founder, time is everything. This tool has increased our development speed by 40% while maintaining code quality."
                    </p>
                    <div class="flex items-center">
                        <div>
                            <h4 class="testimonial-name text-white font-semibold">Emily Watson</h4>
                            <p class="text-gray-400 text-sm">CTO & Co-Founder</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="section-title text-4xl md:text-5xl font-bold mb-6">
                    <span class="gradient-text" data-text="Simple Pricing">
                        Simple Pricing
                    </span>
                </h2>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                    Choose the plan that fits your development needs
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <!-- Free Plan -->
                <div class="pricing-card glass-royal rounded-xl p-8 relative">
                    <div class="text-center">
                        <h3 class="pricing-title text-2xl font-bold mb-4 text-white">Free</h3>
                        <div class="text-4xl font-bold mb-6">
                            <span class="text-3xl text-accent">$</span>0
                            <span class="text-lg text-gray-400">/month</span>
                        </div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Basic code completion
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                100 requests per day
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Community support
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                3 programming languages
                            </li>
                        </ul>
                        <button class="w-full btn-secondary-modern">
                            Get Started
                        </button>
                    </div>
                </div>

                <!-- Pro Plan -->
                <div class="pricing-card popular glass-royal rounded-xl p-8 relative transform scale-105">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-gradient-to-r from-accent to-light-gray text-dark px-4 py-1 rounded-full text-sm font-semibold">
                            Most Popular
                        </span>
                    </div>
                    <div class="text-center">
                        <h3 class="pricing-title text-2xl font-bold mb-4 text-white">Pro</h3>
                        <div class="text-4xl font-bold mb-6">
                            <span class="text-3xl text-accent">$</span>19
                            <span class="text-lg text-gray-400">/month</span>
                        </div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Advanced AI features
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Unlimited requests
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Priority support
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                All programming languages
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Custom models
                            </li>
                        </ul>
                        <button class="w-full btn-primary-modern">
                            Start Free Trial
                        </button>
                    </div>
                </div>

                <!-- Enterprise Plan -->
                <div class="pricing-card glass-royal rounded-xl p-8 relative">
                    <div class="text-center">
                        <h3 class="pricing-title text-2xl font-bold mb-4 text-white">Enterprise</h3>
                        <div class="text-4xl font-bold mb-6">
                            <span class="text-3xl text-accent">$</span>99
                            <span class="text-lg text-gray-400">/month</span>
                        </div>
                        <ul class="text-left space-y-3 mb-8">
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Everything in Pro
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Team collaboration
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Custom integrations
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Dedicated support
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-accent mr-3"></i>
                                Advanced analytics
                            </li>
                        </ul>
                        <button class="w-full btn-secondary-modern">
                            Contact Sales
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 relative">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="section-title text-4xl md:text-5xl font-bold mb-6">
                <span class="gradient-text" data-text="Ready to Transform">
                    Ready to Transform
                </span>
                <br>
                <span class="text-gray-300">Your Coding Experience?</span>
            </h2>
            <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Join thousands of developers who are already coding smarter with AI assistance. Start your free trial today and experience the difference.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button class="btn-primary-modern text-lg px-8 py-4 animate-sexy-bounce">
                    <i class="fas fa-rocket mr-2"></i>
                    Start Your Free Trial
                </button>
                <button class="btn-secondary-modern text-lg px-8 py-4">
                    <i class="fas fa-book mr-2"></i>
                    Read Documentation
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black/40 border-t border-accent/20 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="navbar-logo w-8 h-8 rounded-lg flex items-center justify-center">
                            <i class="fas fa-code text-white text-sm"></i>
                        </div>
                        <span class="navbar-brand text-xl font-bold">
                            AI Code Assistant
                        </span>
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">
                        The intelligent programming partner that helps you write better code faster. Trusted by 50,000+ developers worldwide.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                            <i class="fab fa-discord text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Product</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-accent transition-colors">Features</a></li>
                        <li><a href="#" class="hover:text-accent transition-colors">Pricing</a></li>
                        <li><a href="#" class="hover:text-accent transition-colors">Documentation</a></li>
                        <li><a href="#" class="hover:text-accent transition-colors">API</a></li>
                        <li><a href="#" class="hover:text-accent transition-colors">Integrations</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Company</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-accent transition-colors">About</a></li>
                        <li><a href="#" class="hover:text-accent transition-colors">Blog</a></li>
                        <li><a href="#" class="hover:text-accent transition-colors">Careers</a></li>
                        <li><a href="#" class="hover:text-accent transition-colors">Contact</a></li>
                        <li><a href="#" class="hover:text-accent transition-colors">Press</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-accent/20 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 AI Code Assistant. All rights reserved. | Privacy Policy | Terms of Service</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
