// Modern UI JavaScript - Interactive Functionality

document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Navbar scroll effect
    const navbar = document.querySelector('nav');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add/remove backdrop blur based on scroll
        if (scrollTop > 50) {
            navbar.style.background = 'rgba(15, 15, 35, 0.95)';
            navbar.style.backdropFilter = 'blur(20px)';
        } else {
            navbar.style.background = 'rgba(15, 15, 35, 0.8)';
            navbar.style.backdropFilter = 'blur(10px)';
        }

        lastScrollTop = scrollTop;
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe feature cards for animation
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });

    // Interactive 3D object rotation
    const torusShape = document.querySelector('.torus-shape');
    let isMouseOver = false;
    let rotationSpeed = 1;

    if (torusShape) {
        torusShape.addEventListener('mouseenter', function() {
            isMouseOver = true;
            this.style.animationDuration = '2s';
            this.style.transform = 'scale(1.1) rotateY(0deg) rotateX(15deg)';
        });

        torusShape.addEventListener('mouseleave', function() {
            isMouseOver = false;
            this.style.animationDuration = '8s';
            this.style.transform = 'scale(1) rotateY(0deg) rotateX(15deg)';
        });

        // Mouse movement effect
        torusShape.addEventListener('mousemove', function(e) {
            if (isMouseOver) {
                const rect = this.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                const mouseX = e.clientX - centerX;
                const mouseY = e.clientY - centerY;
                
                const rotateX = (mouseY / rect.height) * 30;
                const rotateY = (mouseX / rect.width) * 30;
                
                this.style.transform = `scale(1.1) rotateY(${rotateY}deg) rotateX(${15 + rotateX}deg)`;
            }
        });
    }

    // Floating elements animation enhancement
    const floatingCards = document.querySelectorAll('.floating-card');
    floatingCards.forEach((card, index) => {
        // Add random movement
        setInterval(() => {
            if (!card.matches(':hover')) {
                const randomX = (Math.random() - 0.5) * 20;
                const randomY = (Math.random() - 0.5) * 20;
                card.style.transform = `translate(${randomX}px, ${randomY}px)`;
            }
        }, 3000 + index * 1000);

        // Reset position on hover
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translate(0, 0) scale(1.05)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translate(0, 0) scale(1)';
        });
    });

    // Button click effects
    const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add CSS for ripple animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    // Parallax effect for background elements
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.floating-card');
        
        parallaxElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = -(scrolled * speed);
            element.style.transform += ` translateY(${yPos}px)`;
        });
    });

    // Material preview interaction
    const materialPreview = document.querySelector('.material-preview');
    if (materialPreview) {
        const materials = [
            'linear-gradient(135deg, #14B8A6, #0D9488)',
            'linear-gradient(135deg, #8B5CF6, #7C3AED)',
            'linear-gradient(135deg, #F59E0B, #D97706)',
            'linear-gradient(135deg, #EF4444, #DC2626)',
            'linear-gradient(135deg, #6366F1, #4F46E5)'
        ];
        
        let currentMaterial = 0;
        
        setInterval(() => {
            currentMaterial = (currentMaterial + 1) % materials.length;
            materialPreview.style.background = materials[currentMaterial];
            
            // Update torus shape material
            if (torusShape) {
                torusShape.style.background = materials[currentMaterial];
                torusShape.style.boxShadow = `
                    0 0 60px ${materials[currentMaterial].includes('#14B8A6') ? 'rgba(20, 184, 166, 0.4)' : 'rgba(139, 92, 246, 0.4)'},
                    inset 0 0 60px ${materials[currentMaterial].includes('#14B8A6') ? 'rgba(20, 184, 166, 0.2)' : 'rgba(139, 92, 246, 0.2)'}
                `;
            }
        }, 3000);
    }

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close any open modals or menus
            const activeElements = document.querySelectorAll('.active, .open');
            activeElements.forEach(element => {
                element.classList.remove('active', 'open');
            });
        }
    });

    // Performance optimization - throttle scroll events
    let ticking = false;
    
    function updateScrollEffects() {
        // Update any scroll-based animations here
        ticking = false;
    }
    
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    });

    // Console log for debugging
    console.log('VisionForge UI loaded successfully!');
    
    // Add loading animation completion
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 100);
});
